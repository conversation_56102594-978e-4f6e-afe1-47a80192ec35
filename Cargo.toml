[package]
name = "libwebsocket-rs"
version = "0.1.0"
rust-version = "1.86.0"
edition = "2024"
build = "build.rs"

[dependencies]
httparse = "1.10.1"
mio = { version = "1.0.1", features = ["net", "os-poll"] }
rand = { version = "0.8.5", features = ["small_rng"] }
rustls = "0.23.26"
webpki-roots = "0.26.8"
perf-macro = { path = "./perf-macro" }
memchr = "2.7.4"
hmac = "0.12.1"
sha2 = "0.10.9"
ed25519-dalek = { version = "2.1.1", features = ["pkcs8", "pem"] }
hex = "0.4.3"
base64 = "0.22.1"
serde_json = "1.0"
libc = "0.2.172"
rustls-pemfile = "2.2.0"
itoa = "1.0.15"
arrayvec = "0.7.6"
chrono = "0.4.41"

[build-dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[[bin]]
name = "arb"
path = "src/bin/arb.rs"

[dev-dependencies]
criterion = "0.6.0"
env_logger = "0.6"
log = "0.4.27"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
url = "2.5.4"

[profile.release]
lto = true
opt-level = 3
codegen-units = 1

[[example]]
name = "http"
path = "examples/http.rs"

[[example]]
name = "ws"
path = "examples/ws.rs"

[[example]]
name = "ws_http"
path = "examples/ws_http.rs"

[[example]]
name = "autobahn-client"
path = "examples/autobahn-client.rs"

[[example]]
name = "sbe_market_data"
path = "examples/sbe_market_data.rs"

[[example]]
name = "sbe_usage_guide"
path = "examples/sbe_usage_guide.rs"

[[example]]
name = "domain_resolver"
path = "examples/domain_resolver.rs"

[[bench]]
name = "order_parse_bench"
path = "benches/order_parse_bench.rs"
harness = false

[[bench]]
name = "book_ticker_parse_bench"
path = "benches/book_ticker_parse_bench.rs"
harness = false

[[bench]]
name = "sbe_performance_bench"
path = "benches/sbe_performance_bench.rs"
harness = false

[[bench]]
name = "generate_order_requests_bench"
path = "benches/generate_order_requests_bench.rs"
harness = false
