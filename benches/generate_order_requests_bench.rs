use criterion::{Criterion, criterion_group, criterion_main};
use libwebsocket_rs::{
    EdgeDirection, ORDER_QUANTITIES, PREDEFINED_RINGS, TRADING_PAIR_RATES,
    engine::trade::{generate_order_requests, generate_order_requests_optimized},
    net::utils::circular_buffer::CircularBuffer,
};
use std::hint::black_box;

// Setup function to initialize test data
fn setup_test_data() {
    // Initialize trading pair rates for testing
    unsafe {
        // Set up some realistic trading pair rates
        for i in 0..28 {
            // Assuming 28 trading pairs based on the code
            TRADING_PAIR_RATES[i][EdgeDirection::Forward as usize] = 1.0 / 50000.0; // ask price = 50000
            TRADING_PAIR_RATES[i][EdgeDirection::Reverse as usize] = 49999.0; // bid price = 49999
        }

        // Set up order quantities
        for i in 0..10 {
            ORDER_QUANTITIES[i] = 0.001; // 0.001 BTC equivalent
        }
    }
}

fn bench_generate_order_requests_single(c: &mut Criterion) {
    setup_test_data();

    c.bench_function("generate_order_requests_single", |b| {
        b.iter(|| {
            const CAP: usize = 4096;
            let mut buf = CircularBuffer::<CAP>::new();
            let now_ns: u64 = 1_755_555_555_000_000;
            let ring_index = 0;
            let edge_index = 0;

            generate_order_requests::<CAP>(
                black_box(ring_index),
                black_box(now_ns),
                black_box(&mut buf),
                black_box(edge_index),
            );

            // Prevent optimization of the buffer
            black_box(buf.len());
        })
    });
}

fn bench_generate_order_requests_optimized(c: &mut Criterion) {
    setup_test_data();

    c.bench_function("generate_order_requests_optimized", |b| {
        b.iter(|| {
            const CAP: usize = 4096;
            let mut buf = CircularBuffer::<CAP>::new();
            let now_ns: u64 = 1_755_555_555_000_000;
            let ring_index = 0;
            let edge_index = 0;

            generate_order_requests_optimized::<CAP>(
                black_box(ring_index),
                black_box(now_ns),
                black_box(&mut buf),
                black_box(edge_index),
            );

            // Prevent optimization of the buffer
            black_box(buf.len());
        })
    });
}

fn bench_generate_order_requests_full_ring(c: &mut Criterion) {
    setup_test_data();

    c.bench_function("generate_order_requests_full_ring", |b| {
        b.iter(|| {
            const CAP: usize = 4096;
            let mut buf = CircularBuffer::<CAP>::new();
            let now_ns: u64 = 1_755_555_555_000_000;
            let ring_index = 0;
            let ring = PREDEFINED_RINGS[ring_index];

            // Generate orders for all edges in the ring (realistic usage)
            for edge_index in 0..ring.len() {
                generate_order_requests::<CAP>(
                    black_box(ring_index),
                    black_box(now_ns + edge_index as u64),
                    black_box(&mut buf),
                    black_box(edge_index),
                );
            }

            // Prevent optimization of the buffer
            black_box(buf.len());
        })
    });
}

fn bench_generate_order_requests_varying_timestamps(c: &mut Criterion) {
    setup_test_data();

    c.bench_function("generate_order_requests_varying_timestamps", |b| {
        let mut counter = 0u64;
        b.iter(|| {
            const CAP: usize = 4096;
            let mut buf = CircularBuffer::<CAP>::new();
            counter += 1;
            let now_ns: u64 = 1_755_555_555_000_000 + counter;
            let ring_index = 0;
            let edge_index = 0;

            generate_order_requests::<CAP>(
                black_box(ring_index),
                black_box(now_ns),
                black_box(&mut buf),
                black_box(edge_index),
            );

            // Prevent optimization of the buffer
            black_box(buf.len());
        })
    });
}

fn bench_generate_order_requests_different_rings(c: &mut Criterion) {
    setup_test_data();

    c.bench_function("generate_order_requests_different_rings", |b| {
        let mut counter = 0usize;
        b.iter(|| {
            const CAP: usize = 4096;
            let mut buf = CircularBuffer::<CAP>::new();
            let now_ns: u64 = 1_755_555_555_000_000;
            let ring_index = counter % PREDEFINED_RINGS.len();
            let edge_index = 0;
            counter += 1;

            generate_order_requests::<CAP>(
                black_box(ring_index),
                black_box(now_ns),
                black_box(&mut buf),
                black_box(edge_index),
            );

            // Prevent optimization of the buffer
            black_box(buf.len());
        })
    });
}

fn bench_generate_order_requests_cpu_cycles(c: &mut Criterion) {
    setup_test_data();

    // This benchmark specifically measures CPU cycles
    let mut group = c.benchmark_group("cpu_cycles");
    group.sample_size(1000);

    group.bench_function("generate_order_requests_cycles", |b| {
        b.iter_custom(|iters| {
            // Use a more portable approach for cycle counting
            let start = std::time::Instant::now();

            for _i in 0..iters {
                const CAP: usize = 4096;
                let mut buf = CircularBuffer::<CAP>::new();
                let now_ns: u64 = 1_755_555_555_000_000;
                let ring_index = 0;
                let edge_index = 0;

                generate_order_requests::<CAP>(
                    black_box(ring_index),
                    black_box(now_ns),
                    black_box(&mut buf),
                    black_box(edge_index),
                );

                // Prevent optimization
                black_box(buf.len());
            }

            start.elapsed()
        });
    });

    group.finish();
}

criterion_group! {
    name = benches;
    config = Criterion::default()
        .sample_size(500)
        .measurement_time(std::time::Duration::from_secs(10));
    targets =
        bench_generate_order_requests_single,
        bench_generate_order_requests_optimized,
        bench_generate_order_requests_full_ring,
        bench_generate_order_requests_varying_timestamps,
        bench_generate_order_requests_different_rings,
        bench_generate_order_requests_cpu_cycles
}

criterion_main!(benches);
