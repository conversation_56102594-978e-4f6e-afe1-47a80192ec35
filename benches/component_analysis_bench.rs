use arrayvec::ArrayVec;
use criterion::{Criterion, criterion_group, criterion_main};
use libwebsocket_rs::{
    EdgeDirection, ORDER_QUANTITIES, PREDEFINED_RINGS, TRADING_PAIR_RATES,
    net::utils::circular_buffer::CircularBuffer,
};
use rand::{RngCore, SeedableRng, rngs::SmallRng};
use std::hint::black_box;
use std::time::{SystemTime, UNIX_EPOCH};

fn setup_test_data() {
    unsafe {
        // 初始化测试数据
        for i in 0..4 {
            ORDER_QUANTITIES[i] = 0.001 + (i as f64) * 0.001;
        }

        // 初始化汇率数据
        for i in 0..16 {
            TRADING_PAIR_RATES[i][EdgeDirection::Forward as usize] = 1.0 + (i as f64) * 0.01;
            TRADING_PAIR_RATES[i][EdgeDirection::Reverse as usize] =
                1.0 / (1.0 + (i as f64) * 0.01);
        }
    }
}

fn bench_system_time_call(c: &mut Criterion) {
    c.bench_function("system_time_call", |b| {
        b.iter(|| {
            let timestamp = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64;
            black_box(timestamp);
        })
    });
}

fn bench_rng_creation(c: &mut Criterion) {
    c.bench_function("rng_creation", |b| {
        b.iter(|| {
            let now_ns: u64 = 1_755_555_555_000_000;
            let mut rng = SmallRng::seed_from_u64(now_ns);
            let mask = rng.next_u32();
            black_box(mask);
        })
    });
}

fn bench_fast_hash_mask(c: &mut Criterion) {
    c.bench_function("fast_hash_mask", |b| {
        b.iter(|| {
            let now_ns: u64 = 1_755_555_555_000_000;
            let timestamp: u64 = 1_755_555_555_000;
            let mask_u32 = ((now_ns as u32) ^ (timestamp as u32)).wrapping_mul(0x9E3779B9);
            black_box(mask_u32);
        })
    });
}

fn bench_string_formatting(c: &mut Criterion) {
    c.bench_function("string_formatting", |b| {
        b.iter(|| {
            let now_ns: u64 = 1_755_555_555_000_000;
            let timestamp: u64 = 1_755_555_555_000;
            let edge_index = 0;
            let ring_index = 0;

            let mut order_id_buf = itoa::Buffer::new();
            let order_id_str = order_id_buf.format(now_ns);
            let mut ts_buf = itoa::Buffer::new();
            let ts_str = ts_buf.format(timestamp);
            let mut edge_index_buf = itoa::Buffer::new();
            let edge_index_str = edge_index_buf.format(edge_index);
            let mut ring_index_buf = itoa::Buffer::new();
            let ring_index_str = ring_index_buf.format(ring_index);

            black_box((order_id_str, ts_str, edge_index_str, ring_index_str));
        })
    });
}

fn bench_json_construction(c: &mut Criterion) {
    setup_test_data();

    c.bench_function("json_construction", |b| {
        b.iter(|| {
            let ring = PREDEFINED_RINGS[0];
            let edge = ring[0];
            let order_id_str = "1755555555000000";
            let ts_str = "1755555555000";
            let edge_index_str = "0";
            let ring_index_str = "0";

            let mut json = ArrayVec::<u8, 256>::new();
            json.try_extend_from_slice(br#"{"id":""#).unwrap();
            json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
            json.try_extend_from_slice(br#"","method":"order.place","params":{"symbol":""#)
                .unwrap();
            json.try_extend_from_slice(edge.0.to_str().as_bytes())
                .unwrap();
            json.try_extend_from_slice(br#"","side":""#).unwrap();
            json.try_extend_from_slice(edge.1.to_str().as_bytes())
                .unwrap();
            json.try_extend_from_slice(br#"","type":"MARKET","newClientOrderId":""#)
                .unwrap();
            json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
            json.push(b'-');
            json.try_extend_from_slice(ring_index_str.as_bytes())
                .unwrap();
            json.push(b'-');
            json.try_extend_from_slice(edge_index_str.as_bytes())
                .unwrap();
            json.push(b'-');
            json.push(b'0');
            json.try_extend_from_slice(br#"","quantity":"0.001","timestamp":"#)
                .unwrap();
            json.try_extend_from_slice(ts_str.as_bytes()).unwrap();
            json.try_extend_from_slice(br#"}}"#).unwrap();

            black_box(json.len());
        })
    });
}

fn bench_websocket_framing(c: &mut Criterion) {
    c.bench_function("websocket_framing", |b| {
        b.iter(|| {
            const CAP: usize = 4096;
            let mut buf = CircularBuffer::<CAP>::new();
            let payload_len = 200; // Typical JSON size
            let mask_u32: u32 = 0x12345678;

            let fin_txt = 0x81u8;
            let mut header = [0u8; 8];
            let mut header_len = 6;
            header[0] = fin_txt;
            let mut second = 0x80u8;

            if payload_len < 126 {
                second |= payload_len as u8;
            }
            header[1] = second;

            let mask_bytes = mask_u32.to_be_bytes();
            buf.put(&header[..2]);
            buf.put(&mask_bytes[..4]);

            // Simulate masking payload
            let dummy_payload = vec![0u8; payload_len];
            for (i, byte) in dummy_payload.iter().enumerate() {
                buf.put_u8(byte ^ mask_bytes[i & 3]);
            }

            black_box(buf.len());
        })
    });
}

criterion_group! {
    name = benches;
    config = Criterion::default()
        .sample_size(1000)
        .measurement_time(std::time::Duration::from_secs(5));
    targets =
        bench_system_time_call,
        bench_rng_creation,
        bench_fast_hash_mask,
        bench_string_formatting,
        bench_json_construction,
        bench_websocket_framing
}

criterion_main!(benches);
