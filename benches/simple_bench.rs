use criterion::{criterion_group, criterion_main, Criterion};
use std::hint::black_box;
use std::time::{SystemTime, UNIX_EPOCH};

fn bench_system_time(c: &mut Criterion) {
    c.bench_function("system_time_call", |b| {
        b.iter(|| {
            let timestamp = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64;
            black_box(timestamp);
        })
    });
}

criterion_group!(benches, bench_system_time);
criterion_main!(benches);
