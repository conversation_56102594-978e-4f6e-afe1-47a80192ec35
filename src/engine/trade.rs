use std::time::{SystemTime, UNIX_EPOCH};

use arrayvec::ArrayVec;
use base64::prelude::*;
use ed25519_dalek::{SigningKey, ed25519::signature::SignerMut, pkcs8::DecodePrivateKey};
use rand::{Rng<PERSON>ore, SeedableRng, rngs::SmallRng};

use crate::{
    EdgeDirection, MAKER_FEE_INDEX, Message, ORDER_FILTER_MIN_ORDER_QTY_INDEX,
    ORDER_FILTER_PRICE_TICK_INDEX, ORDER_FILTERS, ORDER_QUANTITIES, PREDEFINED_RINGS,
    RING_ORDER_RECORDS, TAKER_FEE_INDEX, TRADING_FEES, TRADING_PAIR_RATES, TradingPair,
    engine::trading_pair::PRICE_TICK,
    net::{
        message::http::{HeaderMap, HttpRequest, Method},
        utils::circular_buffer::CircularBuffer,
    },
};

use super::trading_pair::LOT_SIZE_MULT;

/// 根据 price_tick 计算所需的小数位数
fn calculate_decimal_places(price_tick: f64) -> usize {
    if price_tick >= 1.0 {
        0
    } else {
        let mut places = 0;
        let temp = price_tick;
        while (temp * 10.0_f64.powi(places as i32)).fract().abs() > 1e-10 {
            places += 1;
            if places > 10 {
                break;
            } // 防止无限循环
        }
        places
    }
}

/// 格式化价格到指定的 price_tick 精度
fn format_price_with_tick(price: f64, price_tick: f64) -> String {
    // 调整价格到正确的精度
    let adjusted_price = (price / price_tick).round() * price_tick;

    // 计算所需的小数位数
    let decimal_places = calculate_decimal_places(price_tick);

    // 格式化价格到正确的小数位数
    format!("{:.prec$}", adjusted_price, prec = decimal_places)
}

// fn hmac_sign(query: &str, SECRET_KEY: &str) -> String {
//     let mut mac =
//         HmacSha256::new_from_slice(SECRET_KEY.as_bytes()).expect("HMAC can take key of any size");
//     mac.update(query.as_bytes());
//     hex::encode(mac.finalize().into_bytes())
// }

// API keys are now loaded from config.json via build.rs
use crate::engine::trading_pair::{API_KEY as AK, PRIVATE_KEY_PEM as SK_PEM};

fn ed25519_sign(query: &str) -> String {
    let mut signing_key = SigningKey::from_pkcs8_pem(SK_PEM).unwrap();
    let signature = signing_key.sign(query.as_bytes());
    BASE64_STANDARD.encode(signature.to_bytes())
}

pub fn generate_trading_fee_request() -> Message<'static> {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let ts_str = timestamp.to_string();
    let params = format!("timestamp={}", ts_str);
    let signature = ed25519_sign(params.as_str());
    let query = format!("{}&signature={}", params, signature);
    let uri = format!("/sapi/v1/asset/tradeFee?{}", query);
    let mut headers = HeaderMap::new();
    headers.add("X-MBX-APIKEY", AK);
    Message::HttpRequest(HttpRequest {
        method: Method::GET,
        headers: headers,
        uri: uri.leak(),
    })
}

pub fn update_trading_fee(data: &[u8]) {
    let json: serde_json::Value = serde_json::from_slice(data).expect("Invalid JSON format");
    for item in json.as_array().unwrap() {
        let symbol = item["symbol"].as_str().unwrap();
        let maker_fee = item["makerCommission"]
            .as_str()
            .unwrap()
            .parse::<f64>()
            .unwrap();
        let taker_fee = item["takerCommission"]
            .as_str()
            .unwrap()
            .parse::<f64>()
            .unwrap();
        let pair = TradingPair::from(symbol);
        if pair == TradingPair::Unknown {
            continue;
        }
        let pair_idx = pair as usize;
        unsafe {
            TRADING_FEES[pair_idx][MAKER_FEE_INDEX] = maker_fee;
            TRADING_FEES[pair_idx][TAKER_FEE_INDEX] = taker_fee;
            println!("update trading fee: {}: {}, {}", pair, maker_fee, taker_fee);
        }
    }
}

#[inline(always)]
pub fn generate_order_requests<const C: usize>(
    ring_index: usize,
    now_in_ns: u64,
    buf: &mut CircularBuffer<C>,
    edge_index: usize,
) {
    generate_order_requests_optimized(ring_index, now_in_ns, buf, edge_index);
}

#[inline(always)]
pub fn generate_order_requests_optimized<const C: usize>(
    ring_index: usize,
    now_in_ns: u64,
    buf: &mut CircularBuffer<C>,
    edge_index: usize,
) {
    let ring = PREDEFINED_RINGS[ring_index];
    // Use passed timestamp instead of system call for better performance
    let timestamp = (now_in_ns / 1_000_000) as u64; // Convert ns to ms

    // Pre-allocate all buffers to avoid multiple allocations
    let mut order_id_buf = itoa::Buffer::new();
    let order_id_str = order_id_buf.format(now_in_ns);
    let mut ts_buf = itoa::Buffer::new();
    let ts_str = ts_buf.format(timestamp);

    // Use a simpler RNG approach - just use the timestamp for mask
    let mask_u32 = (now_in_ns as u32).wrapping_mul(0x9E3779B9); // Fast hash

    let edge = ring[edge_index];
    let mut edge_index_buf = itoa::Buffer::new();
    let edge_index_str = edge_index_buf.format(edge_index);
    let mut ring_index_buf = itoa::Buffer::new();
    let ring_index_str = ring_index_buf.format(ring_index);

    let mutl = LOT_SIZE_MULT[edge.0 as usize];
    let mut quantity_buf = ArrayVec::<u8, 16>::new();
    let scaled = (unsafe { ORDER_QUANTITIES[edge_index] } * mutl as f64).floor() as u64;
    let mut it = itoa::Buffer::new();
    let digits = it.format(scaled).as_bytes();
    let scale = mutl.ilog10() as usize;

    if mutl == 1 {
        quantity_buf.try_extend_from_slice(digits).unwrap();
    } else if digits.len() <= scale {
        quantity_buf.try_extend_from_slice(b"0.").unwrap();
        for _ in 0..(scale - digits.len()) {
            quantity_buf.try_extend_from_slice(b"0").unwrap();
        }
        quantity_buf.try_extend_from_slice(digits).unwrap();
    } else {
        let int_len = digits.len() - scale;
        quantity_buf
            .try_extend_from_slice(&digits[..int_len])
            .unwrap();
        quantity_buf.try_extend_from_slice(b".").unwrap();
        quantity_buf
            .try_extend_from_slice(&digits[int_len..])
            .unwrap();
    }

    // 计算限价单价格
    // let price = unsafe {
    //     match edge.1 {
    //         EdgeDirection::Forward => {
    //             // 买入时使用 ask 价格（稍微提高以确保成交）
    //             1.0 / TRADING_PAIR_RATES[edge.0 as usize][EdgeDirection::Forward as usize]
    //         }
    //         EdgeDirection::Reverse => {
    //             // 卖出时使用 bid 价格（稍微降低以确保成交）
    //             TRADING_PAIR_RATES[edge.0 as usize][EdgeDirection::Reverse as usize]
    //         }
    //     }
    // };

    // 获取价格精度并格式化价格
    // let price_tick = ORDER_FILTERS[edge.0 as usize][ORDER_FILTER_PRICE_TICK_INDEX];
    // let price_str = format_price_with_tick(price, price_tick);
    // let mut price_buf = ArrayVec::<u8, 32>::new();
    // price_buf
    //     .try_extend_from_slice(price_str.as_bytes())
    //     .unwrap();

    let mut json = ArrayVec::<u8, 256>::new();
    json.try_extend_from_slice(br#"{"id":""#).unwrap();
    json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
    json.try_extend_from_slice(br#"","method":"order.place","params":{"symbol":""#)
        .unwrap();
    json.try_extend_from_slice(edge.0.to_str().as_bytes())
        .unwrap();
    json.try_extend_from_slice(br#"","side":""#).unwrap();
    json.try_extend_from_slice(edge.1.to_str().as_bytes())
        .unwrap();
    // json.try_extend_from_slice(br#"","timeInForce":"IOC"#)
    // .unwrap();
    json.try_extend_from_slice(br#"","type":"MARKET","newClientOrderId":""#)
        .unwrap();
    json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
    json.push(b'-');
    json.try_extend_from_slice(ring_index_str.as_bytes())
        .unwrap();
    json.push(b'-');
    json.try_extend_from_slice(edge_index_str.as_bytes())
        .unwrap();
    json.push(b'-');
    json.push(b'0');
    // json.try_extend_from_slice(br#"","price":""#).unwrap();
    // json.try_extend_from_slice(price_buf.as_slice()).unwrap();
    json.try_extend_from_slice(br#"","quantity":""#).unwrap();
    json.try_extend_from_slice(quantity_buf.as_slice()).unwrap();
    json.try_extend_from_slice(br#"","timestamp":"#).unwrap();
    json.try_extend_from_slice(ts_str.as_bytes()).unwrap();
    json.try_extend_from_slice(br#"}}"#).unwrap();

    let payload_len = json.len();
    let fin_txt = 0x81u8;
    let mut header = [0u8; 8];
    let mut header_len = 6;
    header[0] = fin_txt;
    let mut second = 0x80u8;
    match payload_len {
        len if len < 126 => {
            second |= len as u8;
        }
        len if len <= 65535 => {
            second |= 126;
            let len_bytes = (payload_len as u16).to_be_bytes();
            header[2..4].copy_from_slice(&len_bytes);
            header_len = 8;
        }
        len => {
            panic!("payload_len too long: {}", len);
        }
    }
    header[1] = second;

    let mask_bytes = mask_u32.to_be_bytes();
    match header_len {
        6 => {
            buf.put(&header[..2]);
        }
        8 => {
            buf.put(&header[..4]);
        }
        _ => {
            panic!("header_len: {}", header_len);
        }
    }
    buf.put(&mask_bytes[..4]);

    for (i, byte) in json.iter().enumerate() {
        buf.put_u8(byte ^ mask_bytes[i & 3]);
    }

    unsafe {
        RING_ORDER_RECORDS[edge_index][0] = match edge.1 {
            EdgeDirection::Forward => {
                1.0 / TRADING_PAIR_RATES[edge.0 as usize][EdgeDirection::Forward as usize]
            }
            EdgeDirection::Reverse => {
                TRADING_PAIR_RATES[edge.0 as usize][EdgeDirection::Reverse as usize]
            }
        };
    }
}

pub fn generate_order_request_by_symbol<const N: usize>(
    now_in_ns: u64,
    buf: &mut CircularBuffer<N>,
    price: f64,
    symbol: &str,
) {
    let price = price * 0.98;
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let mut order_id_buf = itoa::Buffer::new();
    let order_id_str = order_id_buf.format(now_in_ns);
    let mut ts_buf = itoa::Buffer::new();
    let ts_str = ts_buf.format(timestamp);

    let mut rng = SmallRng::seed_from_u64(now_in_ns);

    let mut edge_index_buf = itoa::Buffer::new();
    let edge_index_str = edge_index_buf.format(0);
    let mut ring_index_buf = itoa::Buffer::new();
    let ring_index_str = ring_index_buf.format(0);
    let price_tick = PRICE_TICK[TradingPair::from(symbol) as usize];
    let price_str = format_price_with_tick(price, price_tick);

    let mutl = LOT_SIZE_MULT[TradingPair::from(symbol) as usize];
    let min_qty =
        ORDER_FILTERS[TradingPair::from(symbol) as usize][ORDER_FILTER_MIN_ORDER_QTY_INDEX];
    let national_qty = 10.0 / price;
    let qty = if national_qty < min_qty {
        min_qty
    } else {
        national_qty
    };
    let mut quantity_buf = CircularBuffer::<32>::new();
    let scaled = (qty * mutl as f64).floor() as u64;
    let mut it = itoa::Buffer::new();
    let digits = it.format(scaled).as_bytes();
    let scale = mutl.ilog10() as usize;

    if mutl == 1 {
        quantity_buf.put(digits);
    } else if digits.len() <= scale {
        quantity_buf.put(b"0.");
        for _ in 0..(scale - digits.len()) {
            quantity_buf.put(b"0");
        }
        quantity_buf.put(digits);
    } else {
        let int_len = digits.len() - scale;
        quantity_buf.put(&digits[..int_len]);
        quantity_buf.put(b".");
        quantity_buf.put(&digits[int_len..]);
    }

    let mut json = CircularBuffer::<512>::new();
    json.put(br#"{"id":""#);
    json.put(order_id_str.as_bytes());
    json.put(br#"","method":"order.place","params":{"symbol":""#);
    json.put(symbol.as_bytes());
    json.put(br#"","side":""#);
    json.put(b"BUY");
    json.put(br#"","timeInForce":"IOC"#);
    json.put(br#"","type":"LIMIT","newClientOrderId":""#);
    json.put(order_id_str.as_bytes());
    json.put(b"-");
    json.put(ring_index_str.as_bytes());
    json.put(b"-");
    json.put(edge_index_str.as_bytes());
    json.put(b"-");
    json.put(b"1");
    json.put(br#"","price":""#);
    json.put(price_str.as_bytes());
    json.put(br#"","quantity":""#);
    json.put(quantity_buf.as_slices().0);
    json.put(br#"","timestamp":"#);
    json.put(ts_str.as_bytes());
    json.put(br#"}}"#);

    let payload_len = json.len();
    let fin_txt = 0x81u8;
    let mut header = [0u8; 8];
    let mut header_len = 6;
    header[0] = fin_txt;
    let mut second = 0x80u8;
    match payload_len {
        len if len < 126 => {
            second |= len as u8;
        }
        len if len <= 65535 => {
            second |= 126;
            let len_bytes = (payload_len as u16).to_be_bytes();
            header[2..4].copy_from_slice(&len_bytes);
            header_len = 8;
        }
        len => {
            panic!("payload_len too long: {}", len);
        }
    }
    header[1] = second;

    let mask_u32 = rng.next_u32();
    let mask_bytes = mask_u32.to_be_bytes();
    match header_len {
        6 => {
            buf.put(&header[..2]);
        }
        8 => {
            buf.put(&header[..4]);
        }
        _ => {
            panic!("header_len: {}", header_len);
        }
    }
    buf.put(&mask_bytes[..4]);

    for (i, byte) in json.as_slices().0.iter().enumerate() {
        buf.put_u8(byte ^ mask_bytes[i & 3]);
    }
}

pub fn generate_user_data_sub_request() -> String {
    r#"
{
  "id": "d3df8a21-98ea-4fe0-8f4e-0fcea5d418b7",
  "method": "userDataStream.subscribe"
}
"#
    .to_string()
}

pub fn generate_session_logon_request() -> String {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let ts_str = timestamp.to_string();
    let mut params = vec![("apiKey", AK), ("timestamp", ts_str.as_str())];
    params.sort_by(|a, b| a.0.cmp(b.0));
    let param_str = params
        .iter()
        .map(|(k, v)| format!("{}={}", k, v))
        .collect::<Vec<_>>()
        .join("&");
    let signature = ed25519_sign(&param_str);
    let request = format!(
        r#"
{{
  "id": "56374a46-3061-486b-a311-99ee972eb648",
  "method": "session.logon",
  "params": {{
    "apiKey": "{}",
    "signature": "{}",
    "timestamp": {}
    }}
}}
"#,
        AK, signature, timestamp
    );
    request.to_string()
}

#[cfg(test)]
mod tests {
    #[test]
    fn test_ed25519_sign() {
        // let query = "apiKey=iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50&timestamp=1697030400000";
        // let signing_key = SigningKey::from_pkcs8_pem(SK_PEM).unwrap();
        // let verify_key = signing_key.verifying_key();
        // let signature = ed25519_sign(query);
        // assert!(
        //     verify_key
        //         .verify_strict(query.as_bytes(), &signature)
        //         .is_ok()
        // );
    }

    use super::*;
    use serde_json::Value;

    /// 将 CircularBuffer 读出为连续 Vec<u8>
    fn dump_buffer<const C: usize>(buf: &CircularBuffer<C>) -> Vec<u8> {
        let (s1, s2) = buf.as_slices();
        let mut v = Vec::with_capacity(s1.len() + s2.len());
        v.extend_from_slice(s1);
        v.extend_from_slice(s2);
        v
    }

    /// 解析一帧，返回 (payload_json, 总字节数消耗)
    fn parse_frame(raw: &[u8]) -> (Value, usize) {
        assert!(raw.len() >= 6, "frame too small");
        assert_eq!(raw[0], 0x81, "FIN|Text mismatch");
        assert!(raw[1] & 0x80 != 0, "MASK bit not set");
        let (len, mask) = match raw[1] ^ 0x80 {
            0x00 => (raw[2] as usize, &raw[2..6]),
            126 => {
                let len = ((raw[2] as u16) << 8 | raw[3] as u16) as usize;
                (len, &raw[4..8])
            }
            _ => {
                panic!("invalid frame");
            }
        };
        let payload_enc = &raw[8..8 + len];
        assert_eq!(payload_enc.len(), len, "payload len mismatch");

        // 解码
        let mut payload = Vec::with_capacity(len);
        for (i, b) in payload_enc.iter().enumerate() {
            payload.push(b ^ mask[i & 3]);
        }
        let json: Value = serde_json::from_slice(&payload).expect("payload is not valid JSON");
        (json, 8 + len)
    }

    #[test]
    fn test_generate_order_requests_basic() {
        const CAP: usize = 4096;
        let mut buf = CircularBuffer::<CAP>::new();

        // 固定时间戳 => SmallRng 种子固定 => mask 可复现
        let now_ns: u64 = 1_755_555_555_000_000;
        let ring_idx = 0;
        let ring = PREDEFINED_RINGS[ring_idx];

        // 初始化测试用的汇率数据
        for i in 0..ring.len() {
            let pair_idx = ring[i].0 as usize;
            unsafe {
                TRADING_PAIR_RATES[pair_idx][EdgeDirection::Forward as usize] = 1.0 / 50000.0; // ask price = 50000
                TRADING_PAIR_RATES[pair_idx][EdgeDirection::Reverse as usize] = 49999.0; // bid price = 49999
            }
        }

        // Generate orders for all edges in the ring (like in arb.rs)
        for edge_index in 0..ring.len() {
            generate_order_requests::<CAP>(ring_idx, now_ns, &mut buf, edge_index);
        }

        // 1. dump buffer
        let raw = dump_buffer(&buf);

        // 2. 按帧解析
        let mut cursor = 0;
        let mut frame_count = 0;

        while cursor < raw.len() {
            let (json, consumed) = parse_frame(&raw[cursor..]);
            cursor += consumed;
            frame_count += 1;

            // -------- 验证字段 --------
            // id
            assert_eq!(json["id"], now_ns.to_string(), "`id` mismatch");

            // newClientOrderId 前缀
            let ncoi = json["params"]["newClientOrderId"]
                .as_str()
                .expect("no newClientOrderId");
            assert!(
                ncoi.starts_with(&now_ns.to_string()),
                "newClientOrderId prefix mismatch"
            );

            // 验证订单类型为限价单IOC
            assert_eq!(
                json["params"]["type"], "LIMIT",
                "order type should be LIMIT"
            );
            assert_eq!(
                json["params"]["timeInForce"], "IOC",
                "timeInForce should be IOC"
            );

            // 验证价格字段存在
            assert!(
                json["params"]["price"].is_string(),
                "price field should exist and be a string"
            );
            let price_str = json["params"]["price"].as_str().unwrap();
            let price: f64 = price_str.parse().expect("price should be a valid number");
            assert!(price > 0.0, "price should be positive");

            // 验证价格精度符合 price_tick 要求
            let symbol_idx = ring[frame_count - 1].0 as usize;
            let price_tick = ORDER_FILTERS[symbol_idx][ORDER_FILTER_PRICE_TICK_INDEX];
            let price_remainder = (price / price_tick) % 1.0;
            assert!(
                price_remainder.abs() < 1e-10,
                "price {} should be aligned to price_tick {}, remainder: {}",
                price,
                price_tick,
                price_remainder
            );

            // 验证格式化后的价格字符串精度与 price_tick 一致
            let expected_decimal_places = if price_tick >= 1.0 {
                0
            } else {
                (-price_tick.log10()).ceil() as usize
            };

            let actual_decimal_places = if let Some(dot_pos) = price_str.find('.') {
                price_str.len() - dot_pos - 1
            } else {
                0
            };

            assert_eq!(
                actual_decimal_places, expected_decimal_places,
                "Price string '{}' decimal places {} should match price_tick {} expected places {}",
                price_str, actual_decimal_places, price_tick, expected_decimal_places
            );

            // quantity 精度
            let symbol_idx = ring[frame_count - 1].0 as usize;
            let mult = LOT_SIZE_MULT[symbol_idx];
            let qty = json["params"]["quantity"].as_str().unwrap().to_string();
            let scale_expected = mult.ilog10() as usize;
            let scale_actual = qty.split('.').nth(1).map(|s| s.len()).unwrap_or(0);
            assert_eq!(scale_actual, scale_expected, "quantity scale mismatch");
        }

        // 3. 帧数 == ring.len()
        assert_eq!(frame_count, ring.len(), "frame count mismatch");
    }

    #[test]
    fn test_price_formatting_precision() {
        // 测试价格格式化函数的精度处理
        let test_cases = [
            (50000.123456, 0.01, "50000.12"), // 应该格式化为2位小数
            (49999.987654, 0.1, "50000.0"),   // 应该格式化为1位小数
            (50001.5, 1.0, "50002"),          // 应该格式化为整数
            (49998.25, 0.25, "49998.25"),     // 应该保持原精度
            (100.0, 0.001, "100.000"),        // 应该格式化为3位小数
        ];

        for &(price, tick, expected) in test_cases.iter() {
            let price: f64 = price;
            let tick: f64 = tick;

            // 使用与实际代码相同的格式化函数
            let price_str = format_price_with_tick(price, tick);

            assert_eq!(
                price_str, *expected,
                "Price formatting failed: price={}, tick={}, expected={}, actual={}",
                price, tick, expected, price_str
            );
        }
    }
}
